#!/usr/bin/env python3
"""
Analyze sample frames to understand face detection issues
"""

import cv2
import numpy as np
import sys

def analyze_frame(image_path):
    """Analyze a single frame for face detection"""
    print(f"\nAnalyzing: {image_path}")
    
    # Load image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Could not load image: {image_path}")
        return
    
    print(f"Image dimensions: {img.shape}")
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Initialize face detector
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    # Try different parameters for face detection
    print("Trying face detection with different parameters...")
    
    # Standard parameters
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))
    print(f"Standard detection: {len(faces)} faces found")
    
    # More sensitive parameters
    faces2 = face_cascade.detectMultiScale(gray, scaleFactor=1.05, minNeighbors=3, minSize=(20, 20))
    print(f"Sensitive detection: {len(faces2)} faces found")
    
    # Very sensitive parameters
    faces3 = face_cascade.detectMultiScale(gray, scaleFactor=1.02, minNeighbors=2, minSize=(15, 15))
    print(f"Very sensitive detection: {len(faces3)} faces found")
    
    # Use the best detection result
    best_faces = faces3 if len(faces3) > 0 else (faces2 if len(faces2) > 0 else faces)
    
    if len(best_faces) > 0:
        print(f"Best detection found {len(best_faces)} faces:")
        for i, (x, y, w, h) in enumerate(best_faces):
            print(f"  Face {i+1}: x={x}, y={y}, w={w}, h={h}")
            
        # Draw rectangles around detected faces and estimated lip regions
        result_img = img.copy()
        for (x, y, w, h) in best_faces:
            # Draw face rectangle
            cv2.rectangle(result_img, (x, y), (x+w, y+h), (255, 0, 0), 2)
            
            # Estimate and draw lip region
            lip_y_start = y + int(h * 0.65)
            lip_y_end = y + int(h * 0.85)
            lip_x_start = x + int(w * 0.25)
            lip_x_end = x + int(w * 0.75)
            
            cv2.rectangle(result_img, (lip_x_start, lip_y_start), (lip_x_end, lip_y_end), (0, 255, 0), 2)
            
        # Save result
        output_path = image_path.replace('.png', '_analyzed.png')
        cv2.imwrite(output_path, result_img)
        print(f"Analysis result saved to: {output_path}")
    else:
        print("No faces detected with any parameters")
        
        # Let's try a different approach - maybe the image is already cropped to face region
        h, w = img.shape[:2]
        print(f"Assuming entire image is face region ({w}x{h})")
        
        # Estimate lip region for entire image
        lip_y_start = int(h * 0.65)
        lip_y_end = int(h * 0.85)
        lip_x_start = int(w * 0.25)
        lip_x_end = int(w * 0.75)
        
        result_img = img.copy()
        cv2.rectangle(result_img, (lip_x_start, lip_y_start), (lip_x_end, lip_y_end), (0, 255, 0), 2)
        
        output_path = image_path.replace('.png', '_analyzed.png')
        cv2.imwrite(output_path, result_img)
        print(f"Lip region estimation saved to: {output_path}")

def main():
    # Analyze the sample frames
    frames = ['sample_frame_10.png', 'sample_frame_30.png']
    
    for frame in frames:
        try:
            analyze_frame(frame)
        except Exception as e:
            print(f"Error analyzing {frame}: {e}")

if __name__ == "__main__":
    main()
