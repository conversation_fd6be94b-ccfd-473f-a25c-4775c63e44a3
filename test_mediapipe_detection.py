#!/usr/bin/env python3
"""
Test MediaPipe face detection on sample frames
"""

import cv2
import numpy as np
import mediapipe as mp
import sys

def test_mediapipe_on_frame(image_path):
    """Test MediaPipe face mesh on a single frame"""
    print(f"\nTesting MediaPipe on: {image_path}")
    
    # Load image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Could not load image: {image_path}")
        return
    
    print(f"Image dimensions: {img.shape}")
    
    # Initialize MediaPipe Face Mesh
    mp_face_mesh = mp.solutions.face_mesh
    face_mesh = mp_face_mesh.FaceMesh(
        static_image_mode=True,  # Use static mode for single images
        max_num_faces=1,
        refine_landmarks=True,
        min_detection_confidence=0.3,  # Lower threshold
        min_tracking_confidence=0.3
    )
    
    # Convert BGR to RGB
    rgb_img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # Process the image
    results = face_mesh.process(rgb_img)
    
    if results.multi_face_landmarks:
        print(f"✓ Face detected! Found {len(results.multi_face_landmarks)} face(s)")
        
        # Get the first face landmarks
        face_landmarks = results.multi_face_landmarks[0]
        print(f"Total landmarks: {len(face_landmarks.landmark)}")
        
        # Draw landmarks on image
        result_img = img.copy()
        h, w = img.shape[:2]
        
        # Draw all landmarks as small circles
        for idx, landmark in enumerate(face_landmarks.landmark):
            x = int(landmark.x * w)
            y = int(landmark.y * h)
            cv2.circle(result_img, (x, y), 1, (0, 255, 0), -1)
        
        # Highlight lip region landmarks
        lip_landmarks = [
            # Upper lip outer
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
            # Lower lip outer  
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415,
            # Additional lip points
            0, 11, 12, 13, 14, 15, 16, 17, 18, 200, 269, 270, 267, 271, 272
        ]
        
        lip_points = []
        for idx in lip_landmarks:
            if idx < len(face_landmarks.landmark):
                landmark = face_landmarks.landmark[idx]
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                lip_points.append((x, y))
                # Draw lip landmarks in red
                cv2.circle(result_img, (x, y), 2, (0, 0, 255), -1)
        
        if lip_points:
            # Calculate bounding box around lip points
            lip_points = np.array(lip_points)
            x_min, y_min = np.min(lip_points, axis=0)
            x_max, y_max = np.max(lip_points, axis=0)
            
            # Draw bounding box
            cv2.rectangle(result_img, (x_min, y_min), (x_max, y_max), (255, 0, 0), 2)
            
            print(f"Lip bounding box: ({x_min}, {y_min}) to ({x_max}, {y_max})")
            print(f"Lip region size: {x_max - x_min} x {y_max - y_min}")
        
        # Save result
        output_path = image_path.replace('.png', '_mediapipe_test.png')
        cv2.imwrite(output_path, result_img)
        print(f"Result saved to: {output_path}")
        
    else:
        print("✗ No face detected")
        
        # Try with even lower confidence
        face_mesh_low = mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.1,  # Very low threshold
            min_tracking_confidence=0.1
        )
        
        results_low = face_mesh_low.process(rgb_img)
        
        if results_low.multi_face_landmarks:
            print("✓ Face detected with very low confidence threshold!")
        else:
            print("✗ Still no face detected even with very low threshold")
            
            # Let's try face detection instead
            mp_face_detection = mp.solutions.face_detection
            face_detection = mp_face_detection.FaceDetection(
                model_selection=0,  # 0 for short-range (< 2 meters)
                min_detection_confidence=0.1
            )
            
            detection_results = face_detection.process(rgb_img)
            
            if detection_results.detections:
                print(f"✓ Face detection found {len(detection_results.detections)} face(s)")
                
                # Draw detection boxes
                result_img = img.copy()
                h, w = img.shape[:2]
                
                for detection in detection_results.detections:
                    bbox = detection.location_data.relative_bounding_box
                    x = int(bbox.xmin * w)
                    y = int(bbox.ymin * h)
                    width = int(bbox.width * w)
                    height = int(bbox.height * h)
                    
                    cv2.rectangle(result_img, (x, y), (x + width, y + height), (0, 255, 0), 2)
                    print(f"Face detection box: ({x}, {y}) size: {width} x {height}")
                
                output_path = image_path.replace('.png', '_face_detection_test.png')
                cv2.imwrite(output_path, result_img)
                print(f"Face detection result saved to: {output_path}")
            else:
                print("✗ Face detection also failed")

def main():
    # Test on sample frames
    frames = ['sample_frame_10.png', 'sample_frame_30.png']
    
    for frame in frames:
        try:
            test_mediapipe_on_frame(frame)
        except Exception as e:
            print(f"Error testing {frame}: {e}")

if __name__ == "__main__":
    main()
