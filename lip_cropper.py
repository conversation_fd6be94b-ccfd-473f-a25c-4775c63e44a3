#!/usr/bin/env python3
"""
Automated Lip Detection and Cropping for Lipreading Dataset
===========================================================

This script processes video files to detect and crop lip regions for lipreading classification.
It uses OpenCV's face detection and geometric estimation to locate lip regions, then crops
videos to 96x96 pixel frames focused tightly on the lip region.

Requirements:
- opencv-python
- numpy
- ffmpeg (system dependency)

Usage:
    python lip_cropper.py input_video.mp4 [output_video.mp4]
"""

import cv2
import numpy as np
import argparse
import os
import sys
from pathlib import Path
import tempfile
import subprocess
import json


class LipCropper:
    def __init__(self, target_size=(96, 96), padding_factor=0.4):
        """
        Initialize the LipCropper with OpenCV face detection.

        Args:
            target_size: Target output size (width, height)
            padding_factor: Additional padding around detected lip region (0.0-1.0)
        """
        self.target_size = target_size
        self.padding_factor = padding_factor

        # Initialize OpenCV face detector
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        # Try to load additional cascade for profile faces
        try:
            self.profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
        except:
            self.profile_cascade = None

        # Initialize face landmark detector if available
        try:
            # Try to create a simple landmark detector using OpenCV's built-in methods
            self.landmark_detector = None
        except:
            self.landmark_detector = None
        
    def detect_lip_region(self, frame):
        """
        Detect lip region in a frame using OpenCV face detection and geometric estimation.

        Args:
            frame: Input frame (BGR format)

        Returns:
            tuple: (x, y, width, height) of lip bounding box, or None if no face detected
        """
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        h, w = frame.shape[:2]

        # Detect faces using frontal face cascade
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(30, 30)
        )

        # If no frontal faces found, try profile detection
        if len(faces) == 0 and self.profile_cascade is not None:
            faces = self.profile_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )

        if len(faces) == 0:
            # If no face detected, assume the entire frame is a face region
            # This is common for pre-cropped lipreading datasets
            fx, fy, fw, fh = 0, 0, w, h
        else:
            # Use the largest face
            face = max(faces, key=lambda f: f[2] * f[3])
            fx, fy, fw, fh = face

        # Estimate lip region based on face geometry
        # Lips are typically in the lower third of the face
        # and centered horizontally

        # Lip region estimation (based on typical face proportions)
        lip_y_start = fy + int(fh * 0.65)  # Start at 65% down the face
        lip_y_end = fy + int(fh * 0.85)    # End at 85% down the face
        lip_x_start = fx + int(fw * 0.25)  # Start at 25% from left
        lip_x_end = fx + int(fw * 0.75)    # End at 75% from left

        # Add padding
        lip_width = lip_x_end - lip_x_start
        lip_height = lip_y_end - lip_y_start

        padding_w = int(lip_width * self.padding_factor)
        padding_h = int(lip_height * self.padding_factor)

        x_min = max(0, lip_x_start - padding_w)
        y_min = max(0, lip_y_start - padding_h)
        x_max = min(w, lip_x_end + padding_w)
        y_max = min(h, lip_y_end + padding_h)

        return (x_min, y_min, x_max - x_min, y_max - y_min)
    
    def crop_and_resize_frame(self, frame, bbox):
        """
        Crop frame to lip region and resize to target size.
        
        Args:
            frame: Input frame
            bbox: Bounding box (x, y, width, height)
            
        Returns:
            Cropped and resized frame
        """
        x, y, w, h = bbox
        
        # Crop the frame
        cropped = frame[y:y+h, x:x+w]
        
        if cropped.size == 0:
            # Return a black frame if crop failed
            return np.zeros((self.target_size[1], self.target_size[0], 3), dtype=np.uint8)
        
        # Resize to target size
        resized = cv2.resize(cropped, self.target_size, interpolation=cv2.INTER_LANCZOS4)
        
        return resized
    
    def process_video(self, input_path, output_path=None):
        """
        Process a video file to crop lip regions.
        
        Args:
            input_path: Path to input video
            output_path: Path to output video (optional)
            
        Returns:
            Path to processed video
        """
        if output_path is None:
            # Generate output filename
            input_path = Path(input_path)
            output_path = input_path.parent / f"{input_path.stem}_lip_cropped{input_path.suffix}"
        
        # Open input video
        cap = cv2.VideoCapture(str(input_path))
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {input_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Processing video: {input_path}")
        print(f"Total frames: {total_frames}, FPS: {fps}")
        
        # Create temporary file for processed frames
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_video_path = Path(temp_dir) / "temp_output.mp4"
            
            # Initialize video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(
                str(temp_video_path),
                fourcc,
                fps,
                self.target_size
            )
            
            frame_count = 0
            successful_detections = 0
            last_valid_bbox = None
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Detect lip region
                bbox = self.detect_lip_region(frame)
                
                if bbox is not None:
                    last_valid_bbox = bbox
                    successful_detections += 1
                elif last_valid_bbox is not None:
                    # Use last valid bounding box if current detection failed
                    bbox = last_valid_bbox
                else:
                    # If no detection and no previous bbox, use center crop
                    h, w = frame.shape[:2]
                    crop_size = min(h, w) // 2
                    x = (w - crop_size) // 2
                    y = (h - crop_size) // 2
                    bbox = (x, y, crop_size, crop_size)
                
                # Crop and resize frame
                processed_frame = self.crop_and_resize_frame(frame, bbox)
                out.write(processed_frame)
                
                frame_count += 1
                if frame_count % 10 == 0:
                    print(f"Processed {frame_count}/{total_frames} frames")
            
            cap.release()
            out.release()
            
            print(f"Lip detection success rate: {successful_detections}/{total_frames} ({100*successful_detections/total_frames:.1f}%)")
            
            # Use ffmpeg to ensure proper encoding and maintain original audio if present
            self._finalize_video(temp_video_path, input_path, output_path, fps)
        
        return output_path
    
    def _finalize_video(self, temp_video_path, original_path, output_path, fps):
        """
        Use ffmpeg to properly encode the final video.
        """
        cmd = [
            'ffmpeg', '-y',  # Overwrite output file
            '-i', str(temp_video_path),  # Input processed video
            '-c:v', 'libx264',  # Video codec
            '-preset', 'medium',  # Encoding preset
            '-crf', '23',  # Quality setting
            '-pix_fmt', 'yuv420p',  # Pixel format for compatibility
            '-r', str(fps),  # Frame rate
            str(output_path)
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"Successfully created: {output_path}")
        except subprocess.CalledProcessError as e:
            print(f"FFmpeg error: {e}")
            print(f"Command: {' '.join(cmd)}")
            raise


def main():
    parser = argparse.ArgumentParser(description='Crop lip regions from videos for lipreading')
    parser.add_argument('input', help='Input video file')
    parser.add_argument('output', nargs='?', help='Output video file (optional)')
    parser.add_argument('--size', type=int, nargs=2, default=[96, 96], 
                       help='Target size (width height), default: 96 96')
    parser.add_argument('--padding', type=float, default=0.3,
                       help='Padding factor around lips (0.0-1.0), default: 0.3')
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' not found")
        sys.exit(1)
    
    # Create cropper instance
    cropper = LipCropper(target_size=tuple(args.size), padding_factor=args.padding)
    
    try:
        output_path = cropper.process_video(args.input, args.output)
        print(f"\nProcessing complete!")
        print(f"Input: {args.input}")
        print(f"Output: {output_path}")
        
    except Exception as e:
        print(f"Error processing video: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
