#!/usr/bin/env python3
"""
Validation script to compare original and processed videos
"""

import cv2
import numpy as np
import subprocess
import sys

def extract_frame(video_path, frame_number, output_path):
    """Extract a specific frame from video"""
    cmd = [
        'ffmpeg', '-y', '-i', video_path,
        '-vf', f'select=eq(n\\,{frame_number})',
        '-vframes', '1', '-update', '1',
        output_path
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        return True
    except subprocess.CalledProcessError:
        return False

def get_video_info(video_path):
    """Get video information using ffprobe"""
    cmd = [
        'ffprobe', '-v', 'quiet', '-print_format', 'json',
        '-show_format', '-show_streams', video_path
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        import json
        return json.loads(result.stdout)
    except subprocess.CalledProcessError:
        return None

def compare_videos(original_path, processed_path):
    """Compare original and processed videos"""
    print("=== VIDEO COMPARISON REPORT ===\n")
    
    # Get video information
    orig_info = get_video_info(original_path)
    proc_info = get_video_info(processed_path)
    
    if not orig_info or not proc_info:
        print("Error: Could not get video information")
        return
    
    orig_stream = orig_info['streams'][0]
    proc_stream = proc_info['streams'][0]
    
    print(f"ORIGINAL VIDEO: {original_path}")
    print(f"  Resolution: {orig_stream['width']}x{orig_stream['height']}")
    print(f"  Duration: {float(orig_stream['duration']):.2f} seconds")
    print(f"  Frames: {orig_stream['nb_frames']}")
    print(f"  FPS: {float(orig_stream['r_frame_rate'].split('/')[0])/float(orig_stream['r_frame_rate'].split('/')[1]):.2f}")
    print(f"  File size: {int(orig_info['format']['size'])} bytes")
    
    print(f"\nPROCESSED VIDEO: {processed_path}")
    print(f"  Resolution: {proc_stream['width']}x{proc_stream['height']}")
    print(f"  Duration: {float(proc_stream['duration']):.2f} seconds")
    print(f"  Frames: {proc_stream['nb_frames']}")
    print(f"  FPS: {float(proc_stream['r_frame_rate'].split('/')[0])/float(proc_stream['r_frame_rate'].split('/')[1]):.2f}")
    print(f"  File size: {int(proc_info['format']['size'])} bytes")
    
    # Validation checks
    print(f"\n=== VALIDATION RESULTS ===")
    
    # Check resolution
    target_res = (96, 96)
    actual_res = (proc_stream['width'], proc_stream['height'])
    res_check = actual_res == target_res
    print(f"✓ Target resolution (96x96): {'PASS' if res_check else 'FAIL'} - Got {actual_res}")
    
    # Check frame count preservation
    frame_check = orig_stream['nb_frames'] == proc_stream['nb_frames']
    print(f"✓ Frame count preserved: {'PASS' if frame_check else 'FAIL'}")
    
    # Check duration preservation
    orig_duration = float(orig_stream['duration'])
    proc_duration = float(proc_stream['duration'])
    duration_check = abs(orig_duration - proc_duration) < 0.1
    print(f"✓ Duration preserved: {'PASS' if duration_check else 'FAIL'}")
    
    # Extract sample frames for visual comparison
    print(f"\n=== EXTRACTING SAMPLE FRAMES ===")
    
    frames_to_check = [10, 30, 50]
    total_frames = int(orig_stream['nb_frames'])
    
    for frame_num in frames_to_check:
        if frame_num < total_frames:
            orig_frame_path = f"orig_frame_{frame_num}.png"
            proc_frame_path = f"proc_frame_{frame_num}.png"
            
            if extract_frame(original_path, frame_num, orig_frame_path):
                if extract_frame(processed_path, frame_num, proc_frame_path):
                    print(f"✓ Extracted frame {frame_num}: {orig_frame_path} -> {proc_frame_path}")
                else:
                    print(f"✗ Failed to extract processed frame {frame_num}")
            else:
                print(f"✗ Failed to extract original frame {frame_num}")
    
    # Overall assessment
    print(f"\n=== OVERALL ASSESSMENT ===")
    all_checks = [res_check, frame_check, duration_check]
    
    if all(all_checks):
        print("🎉 SUCCESS: All validation checks passed!")
        print("   - Video successfully cropped to 96x96 pixels")
        print("   - Frame count and duration preserved")
        print("   - Ready for lipreading classifier training")
    else:
        print("⚠️  WARNING: Some validation checks failed")
        print("   Please review the results above")
    
    return all(all_checks)

def main():
    if len(sys.argv) != 3:
        print("Usage: python validate_results.py <original_video> <processed_video>")
        sys.exit(1)
    
    original_path = sys.argv[1]
    processed_path = sys.argv[2]
    
    # Check if files exist
    import os
    if not os.path.exists(original_path):
        print(f"Error: Original video not found: {original_path}")
        sys.exit(1)
    
    if not os.path.exists(processed_path):
        print(f"Error: Processed video not found: {processed_path}")
        sys.exit(1)
    
    success = compare_videos(original_path, processed_path)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
