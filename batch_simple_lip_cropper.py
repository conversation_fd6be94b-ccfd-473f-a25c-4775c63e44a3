#!/usr/bin/env python3
"""
Batch processing script for simple lip cropping
Processes all MP4 videos in a directory using rectangular crop approach
"""

import os
import sys
import argparse
from pathlib import Path
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from simple_lip_cropper import SimpleLipCropper

class SimpleBatchProcessor:
    def __init__(self, input_dir, output_dir=None, target_size=(96, 96), max_workers=4, crop_params=None):
        """
        Initialize the batch processor.
        
        Args:
            input_dir: Directory containing input videos
            output_dir: Directory for output videos (default: input_dir + '_simple_lip_cropped')
            target_size: Target output size (width, height)
            max_workers: Number of parallel processing threads
            crop_params: Custom crop parameters dict
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir) if output_dir else Path(str(input_dir) + '_simple_lip_cropped')
        self.target_size = target_size
        self.max_workers = max_workers
        self.crop_params = crop_params
        
        # Create output directory
        self.output_dir.mkdir(exist_ok=True)
        
        # Statistics
        self.stats = {
            'total_videos': 0,
            'processed': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': None,
            'end_time': None
        }
        
        # Thread-safe logging
        self.log_lock = threading.Lock()
        
    def find_videos(self):
        """Find all MP4 videos in input directory"""
        video_files = list(self.input_dir.glob('*.mp4'))
        self.stats['total_videos'] = len(video_files)
        return video_files
    
    def get_output_path(self, input_path):
        """Generate output path preserving filename structure"""
        stem = input_path.stem
        suffix = input_path.suffix
        output_filename = f"{stem}_simple_lip_cropped{suffix}"
        return self.output_dir / output_filename
    
    def log_message(self, message):
        """Thread-safe logging"""
        with self.log_lock:
            print(f"[{time.strftime('%H:%M:%S')}] {message}")
    
    def process_single_video(self, input_path):
        """Process a single video file"""
        try:
            output_path = self.get_output_path(input_path)
            
            # Skip if output already exists
            if output_path.exists():
                self.log_message(f"SKIP: {input_path.name} (output exists)")
                self.stats['skipped'] += 1
                return True
            
            # Create cropper instance
            cropper = SimpleLipCropper(target_size=self.target_size)
            
            # Apply custom crop parameters if provided
            if self.crop_params:
                cropper.crop_params.update(self.crop_params)
            
            self.log_message(f"START: {input_path.name}")
            start_time = time.time()
            
            # Process the video (suppress detailed output for batch processing)
            import io
            import contextlib
            
            # Capture stdout to reduce noise during batch processing
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                result_path = cropper.process_video(str(input_path), str(output_path))
            
            processing_time = time.time() - start_time
            
            # Verify output
            if output_path.exists() and output_path.stat().st_size > 0:
                self.log_message(f"SUCCESS: {input_path.name} -> {output_path.name} ({processing_time:.1f}s)")
                self.stats['processed'] += 1
                return True
            else:
                self.log_message(f"FAILED: {input_path.name} (no output generated)")
                self.stats['failed'] += 1
                return False
                
        except Exception as e:
            self.log_message(f"ERROR: {input_path.name} - {str(e)}")
            self.stats['failed'] += 1
            return False
    
    def process_dataset(self):
        """Process entire dataset"""
        self.log_message("Starting simple lip cropping batch processing...")
        self.log_message(f"Input directory: {self.input_dir}")
        self.log_message(f"Output directory: {self.output_dir}")
        self.log_message(f"Target size: {self.target_size}")
        self.log_message(f"Max workers: {self.max_workers}")
        if self.crop_params:
            self.log_message(f"Custom crop params: {self.crop_params}")
        
        # Find all videos
        video_files = self.find_videos()
        
        if not video_files:
            self.log_message("No MP4 videos found in input directory")
            return
        
        self.log_message(f"Found {len(video_files)} videos to process")
        
        self.stats['start_time'] = time.time()
        
        # Process videos in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all jobs
            future_to_video = {
                executor.submit(self.process_single_video, video_path): video_path
                for video_path in video_files
            }
            
            # Process completed jobs
            for future in as_completed(future_to_video):
                video_path = future_to_video[future]
                try:
                    success = future.result()
                except Exception as e:
                    self.log_message(f"EXCEPTION: {video_path.name} - {str(e)}")
                    self.stats['failed'] += 1
        
        self.stats['end_time'] = time.time()
        self.print_summary()
    
    def print_summary(self):
        """Print processing summary"""
        total_time = self.stats['end_time'] - self.stats['start_time']
        
        print("\n" + "="*60)
        print("SIMPLE LIP CROPPING - PROCESSING SUMMARY")
        print("="*60)
        print(f"Total videos found: {self.stats['total_videos']}")
        print(f"Successfully processed: {self.stats['processed']}")
        print(f"Skipped (already exist): {self.stats['skipped']}")
        print(f"Failed: {self.stats['failed']}")
        print(f"Total processing time: {total_time:.1f} seconds")
        
        if self.stats['processed'] > 0:
            avg_time = total_time / self.stats['processed']
            print(f"Average time per video: {avg_time:.1f} seconds")
        
        success_rate = (self.stats['processed'] / self.stats['total_videos']) * 100 if self.stats['total_videos'] > 0 else 0
        print(f"Success rate: {success_rate:.1f}%")
        
        if self.stats['failed'] == 0:
            print("\n🎉 All videos processed successfully!")
            print("✓ Rectangular crop approach worked perfectly")
            print("✓ All videos cropped to focus on lip region")
            print("✓ Ready for lipreading classifier training")
        elif self.stats['failed'] < self.stats['total_videos'] * 0.1:
            print(f"\n✅ Processing mostly successful ({self.stats['failed']} failures)")
        else:
            print(f"\n⚠️  High failure rate: {self.stats['failed']} videos failed")
        
        print("="*60)
        
        # Save processing log
        log_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'method': 'simple_rectangular_crop',
            'input_directory': str(self.input_dir),
            'output_directory': str(self.output_dir),
            'settings': {
                'target_size': self.target_size,
                'max_workers': self.max_workers,
                'crop_params': self.crop_params
            },
            'statistics': self.stats
        }
        
        log_file = self.output_dir / 'simple_processing_log.json'
        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2)
        
        print(f"Processing log saved to: {log_file}")

def main():
    parser = argparse.ArgumentParser(description='Batch process videos with simple rectangular lip cropping')
    parser.add_argument('input_dir', help='Input directory containing MP4 videos')
    parser.add_argument('--output_dir', help='Output directory (default: input_dir + "_simple_lip_cropped")')
    parser.add_argument('--size', type=int, nargs=2, default=[96, 96],
                       help='Target size (width height), default: 96 96')
    parser.add_argument('--workers', type=int, default=4,
                       help='Number of parallel workers, default: 4')
    parser.add_argument('--y-start', type=float, default=0.55,
                       help='Y start ratio (0.0-1.0), default: 0.55')
    parser.add_argument('--y-end', type=float, default=0.95,
                       help='Y end ratio (0.0-1.0), default: 0.95')
    parser.add_argument('--x-start', type=float, default=0.15,
                       help='X start ratio (0.0-1.0), default: 0.15')
    parser.add_argument('--x-end', type=float, default=0.85,
                       help='X end ratio (0.0-1.0), default: 0.85')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be processed without actually processing')
    
    args = parser.parse_args()
    
    # Validate input directory
    if not os.path.exists(args.input_dir):
        print(f"Error: Input directory '{args.input_dir}' not found")
        sys.exit(1)
    
    if not os.path.isdir(args.input_dir):
        print(f"Error: '{args.input_dir}' is not a directory")
        sys.exit(1)
    
    # Prepare crop parameters
    crop_params = {
        'y_start_ratio': args.y_start,
        'y_end_ratio': args.y_end,
        'x_start_ratio': args.x_start,
        'x_end_ratio': args.x_end,
    }
    
    # Create processor
    processor = SimpleBatchProcessor(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        target_size=tuple(args.size),
        max_workers=args.workers,
        crop_params=crop_params
    )
    
    if args.dry_run:
        # Show what would be processed
        video_files = processor.find_videos()
        print(f"DRY RUN: Would process {len(video_files)} videos")
        print(f"Input: {processor.input_dir}")
        print(f"Output: {processor.output_dir}")
        print(f"Settings: {args.size[0]}x{args.size[1]}, workers={args.workers}")
        print(f"Crop params: {crop_params}")
        
        if video_files:
            print("\nFirst 10 videos that would be processed:")
            for video in video_files[:10]:
                output_path = processor.get_output_path(video)
                status = "EXISTS" if output_path.exists() else "NEW"
                print(f"  {video.name} -> {output_path.name} [{status}]")
    else:
        # Process the dataset
        processor.process_dataset()

if __name__ == "__main__":
    main()
