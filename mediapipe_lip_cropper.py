#!/usr/bin/env python3
"""
MediaPipe-based Lip Detection and Cropping for Lipreading Dataset
================================================================

This script uses MediaPipe Face Mesh for precise lip landmark detection
and crops videos to 96x96 pixel frames focused tightly on the lip region.

Requirements:
- opencv-python
- mediapipe
- numpy
- ffmpeg (system dependency)

Usage:
    python mediapipe_lip_cropper.py input_video.mp4 [output_video.mp4]
"""

import cv2
import numpy as np
import mediapipe as mp
import argparse
import os
import sys
from pathlib import Path
import tempfile
import subprocess


class MediaPipeLipCropper:
    def __init__(self, target_size=(96, 96), padding_factor=0.2):
        """
        Initialize the MediaPipe-based LipCropper.
        
        Args:
            target_size: Target output size (width, height)
            padding_factor: Additional padding around detected lip region (0.0-1.0)
        """
        self.target_size = target_size
        self.padding_factor = padding_factor
        
        # Initialize MediaPipe Face Mesh
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # MediaPipe Face Mesh lip landmark indices (468 landmarks total)
        # These are the specific indices for lip contours in MediaPipe's face mesh
        self.UPPER_LIP_OUTER = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318]
        self.LOWER_LIP_OUTER = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415]
        self.UPPER_LIP_INNER = [78, 81, 80, 78, 82, 13, 312, 311, 310, 415]
        self.LOWER_LIP_INNER = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415]
        
        # Combined comprehensive lip region
        self.LIP_LANDMARKS = list(set(
            self.UPPER_LIP_OUTER + self.LOWER_LIP_OUTER + 
            self.UPPER_LIP_INNER + self.LOWER_LIP_INNER +
            # Additional lip region landmarks for better coverage
            [0, 11, 12, 13, 14, 15, 16, 17, 18, 200, 269, 270, 267, 271, 272]
        ))
        
        # Fallback: if MediaPipe fails, use these proportional estimates
        self.fallback_lip_region = {
            'y_start_ratio': 0.6,   # Start at 60% down the face
            'y_end_ratio': 0.85,    # End at 85% down the face  
            'x_start_ratio': 0.2,   # Start at 20% from left
            'x_end_ratio': 0.8      # End at 80% from left
        }
        
    def detect_lip_region(self, frame):
        """
        Detect lip region using MediaPipe Face Mesh.
        
        Args:
            frame: Input frame (BGR format)
            
        Returns:
            tuple: (x, y, width, height) of lip bounding box, or None if detection fails
        """
        # Convert BGR to RGB for MediaPipe
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.face_mesh.process(rgb_frame)
        
        h, w = frame.shape[:2]
        
        if results.multi_face_landmarks:
            # Get the first face landmarks
            face_landmarks = results.multi_face_landmarks[0]
            
            # Extract lip landmark coordinates
            lip_points = []
            for idx in self.LIP_LANDMARKS:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = int(landmark.x * w)
                    y = int(landmark.y * h)
                    # Ensure coordinates are within frame bounds
                    x = max(0, min(w-1, x))
                    y = max(0, min(h-1, y))
                    lip_points.append((x, y))
            
            if len(lip_points) >= 4:  # Need at least 4 points for a bounding box
                # Calculate bounding box around lip points
                lip_points = np.array(lip_points)
                x_min, y_min = np.min(lip_points, axis=0)
                x_max, y_max = np.max(lip_points, axis=0)
                
                # Add padding
                width = x_max - x_min
                height = y_max - y_min
                
                # Ensure minimum size
                min_size = min(w, h) // 8  # At least 1/8 of the smaller dimension
                if width < min_size:
                    center_x = (x_min + x_max) // 2
                    x_min = center_x - min_size // 2
                    x_max = center_x + min_size // 2
                    width = min_size
                    
                if height < min_size:
                    center_y = (y_min + y_max) // 2
                    y_min = center_y - min_size // 2
                    y_max = center_y + min_size // 2
                    height = min_size
                
                # Apply padding
                padding_w = int(width * self.padding_factor)
                padding_h = int(height * self.padding_factor)
                
                x_min = max(0, x_min - padding_w)
                y_min = max(0, y_min - padding_h)
                x_max = min(w, x_max + padding_w)
                y_max = min(h, y_max + padding_h)
                
                return (x_min, y_min, x_max - x_min, y_max - y_min)
        
        # Fallback: assume entire frame is face and estimate lip region
        print("MediaPipe detection failed, using fallback estimation")
        lip_y_start = int(h * self.fallback_lip_region['y_start_ratio'])
        lip_y_end = int(h * self.fallback_lip_region['y_end_ratio'])
        lip_x_start = int(w * self.fallback_lip_region['x_start_ratio'])
        lip_x_end = int(w * self.fallback_lip_region['x_end_ratio'])
        
        # Add padding to fallback region
        lip_width = lip_x_end - lip_x_start
        lip_height = lip_y_end - lip_y_start
        
        padding_w = int(lip_width * self.padding_factor)
        padding_h = int(lip_height * self.padding_factor)
        
        x_min = max(0, lip_x_start - padding_w)
        y_min = max(0, lip_y_start - padding_h)
        x_max = min(w, lip_x_end + padding_w)
        y_max = min(h, lip_y_end + padding_h)
        
        return (x_min, y_min, x_max - x_min, y_max - y_min)
    
    def crop_and_resize_frame(self, frame, bbox):
        """
        Crop frame to lip region and resize to target size.
        
        Args:
            frame: Input frame
            bbox: Bounding box (x, y, width, height)
            
        Returns:
            Cropped and resized frame
        """
        x, y, w, h = bbox
        
        # Ensure valid crop region
        if w <= 0 or h <= 0:
            return np.zeros((self.target_size[1], self.target_size[0], 3), dtype=np.uint8)
        
        # Crop the frame
        cropped = frame[y:y+h, x:x+w]
        
        if cropped.size == 0:
            return np.zeros((self.target_size[1], self.target_size[0], 3), dtype=np.uint8)
        
        # Make crop square by padding if necessary
        crop_h, crop_w = cropped.shape[:2]
        if crop_h != crop_w:
            # Pad to make square
            max_dim = max(crop_h, crop_w)
            pad_h = (max_dim - crop_h) // 2
            pad_w = (max_dim - crop_w) // 2
            
            cropped = cv2.copyMakeBorder(
                cropped, pad_h, max_dim - crop_h - pad_h, 
                pad_w, max_dim - crop_w - pad_w, 
                cv2.BORDER_CONSTANT, value=[0, 0, 0]
            )
        
        # Resize to target size using high-quality interpolation
        resized = cv2.resize(cropped, self.target_size, interpolation=cv2.INTER_LANCZOS4)
        
        return resized
    
    def process_video(self, input_path, output_path=None):
        """
        Process a video file to crop lip regions using MediaPipe.
        
        Args:
            input_path: Path to input video
            output_path: Path to output video (optional)
            
        Returns:
            Path to processed video
        """
        if output_path is None:
            input_path = Path(input_path)
            output_path = input_path.parent / f"{input_path.stem}_mediapipe_lip_cropped{input_path.suffix}"
        
        # Open input video
        cap = cv2.VideoCapture(str(input_path))
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {input_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Processing video: {input_path}")
        print(f"Total frames: {total_frames}, FPS: {fps}")
        
        # Create temporary file for processed frames
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_video_path = Path(temp_dir) / "temp_output.mp4"
            
            # Initialize video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(
                str(temp_video_path),
                fourcc,
                fps,
                self.target_size
            )
            
            frame_count = 0
            successful_detections = 0
            last_valid_bbox = None
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Detect lip region
                bbox = self.detect_lip_region(frame)
                
                if bbox is not None:
                    # Validate bbox
                    x, y, w, h = bbox
                    if w > 0 and h > 0:
                        last_valid_bbox = bbox
                        successful_detections += 1
                    else:
                        bbox = last_valid_bbox
                elif last_valid_bbox is not None:
                    # Use last valid bounding box if current detection failed
                    bbox = last_valid_bbox
                else:
                    # Emergency fallback: center crop
                    frame_h, frame_w = frame.shape[:2]
                    crop_size = min(frame_h, frame_w) // 2
                    x = (frame_w - crop_size) // 2
                    y = (frame_h - crop_size) // 2
                    bbox = (x, y, crop_size, crop_size)
                
                # Crop and resize frame
                processed_frame = self.crop_and_resize_frame(frame, bbox)
                out.write(processed_frame)
                
                frame_count += 1
                if frame_count % 10 == 0:
                    print(f"Processed {frame_count}/{total_frames} frames")
            
            cap.release()
            out.release()
            
            detection_rate = (successful_detections / total_frames) * 100 if total_frames > 0 else 0
            print(f"MediaPipe lip detection success rate: {successful_detections}/{total_frames} ({detection_rate:.1f}%)")
            
            # Use ffmpeg to ensure proper encoding
            self._finalize_video(temp_video_path, input_path, output_path, fps)
        
        return output_path
    
    def _finalize_video(self, temp_video_path, original_path, output_path, fps):
        """Use ffmpeg to properly encode the final video."""
        cmd = [
            'ffmpeg', '-y',  # Overwrite output file
            '-i', str(temp_video_path),  # Input processed video
            '-c:v', 'libx264',  # Video codec
            '-preset', 'medium',  # Encoding preset
            '-crf', '23',  # Quality setting
            '-pix_fmt', 'yuv420p',  # Pixel format for compatibility
            '-r', str(fps),  # Frame rate
            str(output_path)
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"Successfully created: {output_path}")
        except subprocess.CalledProcessError as e:
            print(f"FFmpeg error: {e}")
            raise


def main():
    parser = argparse.ArgumentParser(description='Crop lip regions using MediaPipe Face Mesh')
    parser.add_argument('input', help='Input video file')
    parser.add_argument('output', nargs='?', help='Output video file (optional)')
    parser.add_argument('--size', type=int, nargs=2, default=[96, 96], 
                       help='Target size (width height), default: 96 96')
    parser.add_argument('--padding', type=float, default=0.2,
                       help='Padding factor around lips (0.0-1.0), default: 0.2')
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' not found")
        sys.exit(1)
    
    # Create cropper instance
    cropper = MediaPipeLipCropper(target_size=tuple(args.size), padding_factor=args.padding)
    
    try:
        output_path = cropper.process_video(args.input, args.output)
        print(f"\nProcessing complete!")
        print(f"Input: {args.input}")
        print(f"Output: {output_path}")
        
    except Exception as e:
        print(f"Error processing video: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
