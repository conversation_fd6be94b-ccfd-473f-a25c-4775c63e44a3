#!/usr/bin/env python3
"""
Batch processing script for lipreading dataset
Processes all MP4 videos in a directory to crop lip regions to 96x96 pixels
"""

import os
import sys
import argparse
from pathlib import Path
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from lip_cropper import LipCropper

class DatasetProcessor:
    def __init__(self, input_dir, output_dir=None, target_size=(96, 96), padding_factor=0.4, max_workers=4):
        """
        Initialize the dataset processor.
        
        Args:
            input_dir: Directory containing input videos
            output_dir: Directory for output videos (default: input_dir + '_lip_cropped')
            target_size: Target output size (width, height)
            padding_factor: Padding around lip region
            max_workers: Number of parallel processing threads
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir) if output_dir else Path(str(input_dir) + '_lip_cropped')
        self.target_size = target_size
        self.padding_factor = padding_factor
        self.max_workers = max_workers
        
        # Create output directory
        self.output_dir.mkdir(exist_ok=True)
        
        # Statistics
        self.stats = {
            'total_videos': 0,
            'processed': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': None,
            'end_time': None
        }
        
        # Thread-safe logging
        self.log_lock = threading.Lock()
        
    def find_videos(self):
        """Find all MP4 videos in input directory"""
        video_files = list(self.input_dir.glob('*.mp4'))
        self.stats['total_videos'] = len(video_files)
        return video_files
    
    def get_output_path(self, input_path):
        """Generate output path preserving filename structure"""
        # Keep original filename but add suffix before extension
        stem = input_path.stem
        suffix = input_path.suffix
        output_filename = f"{stem}_lip_cropped{suffix}"
        return self.output_dir / output_filename
    
    def log_message(self, message):
        """Thread-safe logging"""
        with self.log_lock:
            print(f"[{time.strftime('%H:%M:%S')}] {message}")
    
    def process_single_video(self, input_path):
        """Process a single video file"""
        try:
            output_path = self.get_output_path(input_path)
            
            # Skip if output already exists
            if output_path.exists():
                self.log_message(f"SKIP: {input_path.name} (output exists)")
                self.stats['skipped'] += 1
                return True
            
            # Create cropper instance
            cropper = LipCropper(
                target_size=self.target_size,
                padding_factor=self.padding_factor
            )
            
            self.log_message(f"START: {input_path.name}")
            start_time = time.time()
            
            # Process the video
            result_path = cropper.process_video(str(input_path), str(output_path))
            
            processing_time = time.time() - start_time
            
            # Verify output
            if output_path.exists() and output_path.stat().st_size > 0:
                self.log_message(f"SUCCESS: {input_path.name} -> {output_path.name} ({processing_time:.1f}s)")
                self.stats['processed'] += 1
                return True
            else:
                self.log_message(f"FAILED: {input_path.name} (no output generated)")
                self.stats['failed'] += 1
                return False
                
        except Exception as e:
            self.log_message(f"ERROR: {input_path.name} - {str(e)}")
            self.stats['failed'] += 1
            return False
    
    def process_dataset(self):
        """Process entire dataset"""
        self.log_message("Starting dataset processing...")
        self.log_message(f"Input directory: {self.input_dir}")
        self.log_message(f"Output directory: {self.output_dir}")
        self.log_message(f"Target size: {self.target_size}")
        self.log_message(f"Padding factor: {self.padding_factor}")
        self.log_message(f"Max workers: {self.max_workers}")
        
        # Find all videos
        video_files = self.find_videos()
        
        if not video_files:
            self.log_message("No MP4 videos found in input directory")
            return
        
        self.log_message(f"Found {len(video_files)} videos to process")
        
        self.stats['start_time'] = time.time()
        
        # Process videos in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all jobs
            future_to_video = {
                executor.submit(self.process_single_video, video_path): video_path
                for video_path in video_files
            }
            
            # Process completed jobs
            for future in as_completed(future_to_video):
                video_path = future_to_video[future]
                try:
                    success = future.result()
                except Exception as e:
                    self.log_message(f"EXCEPTION: {video_path.name} - {str(e)}")
                    self.stats['failed'] += 1
        
        self.stats['end_time'] = time.time()
        self.print_summary()
    
    def print_summary(self):
        """Print processing summary"""
        total_time = self.stats['end_time'] - self.stats['start_time']
        
        print("\n" + "="*60)
        print("PROCESSING SUMMARY")
        print("="*60)
        print(f"Total videos found: {self.stats['total_videos']}")
        print(f"Successfully processed: {self.stats['processed']}")
        print(f"Skipped (already exist): {self.stats['skipped']}")
        print(f"Failed: {self.stats['failed']}")
        print(f"Total processing time: {total_time:.1f} seconds")
        
        if self.stats['processed'] > 0:
            avg_time = total_time / self.stats['processed']
            print(f"Average time per video: {avg_time:.1f} seconds")
        
        success_rate = (self.stats['processed'] / self.stats['total_videos']) * 100 if self.stats['total_videos'] > 0 else 0
        print(f"Success rate: {success_rate:.1f}%")
        
        if self.stats['failed'] == 0:
            print("\n🎉 All videos processed successfully!")
        elif self.stats['failed'] < self.stats['total_videos'] * 0.1:
            print(f"\n✅ Processing mostly successful ({self.stats['failed']} failures)")
        else:
            print(f"\n⚠️  High failure rate: {self.stats['failed']} videos failed")
        
        print("="*60)
        
        # Save processing log
        log_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'input_directory': str(self.input_dir),
            'output_directory': str(self.output_dir),
            'settings': {
                'target_size': self.target_size,
                'padding_factor': self.padding_factor,
                'max_workers': self.max_workers
            },
            'statistics': self.stats
        }
        
        log_file = self.output_dir / 'processing_log.json'
        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2)
        
        print(f"Processing log saved to: {log_file}")

def main():
    parser = argparse.ArgumentParser(description='Batch process lipreading dataset')
    parser.add_argument('input_dir', help='Input directory containing MP4 videos')
    parser.add_argument('--output_dir', help='Output directory (default: input_dir + "_lip_cropped")')
    parser.add_argument('--size', type=int, nargs=2, default=[96, 96],
                       help='Target size (width height), default: 96 96')
    parser.add_argument('--padding', type=float, default=0.4,
                       help='Padding factor around lips (0.0-1.0), default: 0.4')
    parser.add_argument('--workers', type=int, default=4,
                       help='Number of parallel workers, default: 4')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be processed without actually processing')
    
    args = parser.parse_args()
    
    # Validate input directory
    if not os.path.exists(args.input_dir):
        print(f"Error: Input directory '{args.input_dir}' not found")
        sys.exit(1)
    
    if not os.path.isdir(args.input_dir):
        print(f"Error: '{args.input_dir}' is not a directory")
        sys.exit(1)
    
    # Create processor
    processor = DatasetProcessor(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        target_size=tuple(args.size),
        padding_factor=args.padding,
        max_workers=args.workers
    )
    
    if args.dry_run:
        # Show what would be processed
        video_files = processor.find_videos()
        print(f"DRY RUN: Would process {len(video_files)} videos")
        print(f"Input: {processor.input_dir}")
        print(f"Output: {processor.output_dir}")
        print(f"Settings: {args.size[0]}x{args.size[1]}, padding={args.padding}, workers={args.workers}")
        
        if video_files:
            print("\nFirst 10 videos that would be processed:")
            for video in video_files[:10]:
                output_path = processor.get_output_path(video)
                status = "EXISTS" if output_path.exists() else "NEW"
                print(f"  {video.name} -> {output_path.name} [{status}]")
    else:
        # Process the dataset
        processor.process_dataset()

if __name__ == "__main__":
    main()
