#!/usr/bin/env python3
"""
Simple Rectangular Lip Cropper for Pre-cropped Face Videos
==========================================================

This script uses a simple rectangular crop approach optimized for videos that are
already tightly cropped to face regions. It focuses on the lower portion of the
face where lips are located.

Requirements:
- opencv-python
- numpy
- ffmpeg (system dependency)

Usage:
    python simple_lip_cropper.py input_video.mp4 [output_video.mp4]
"""

import cv2
import numpy as np
import argparse
import os
import sys
from pathlib import Path
import tempfile
import subprocess


class SimpleLipCropper:
    def __init__(self, target_size=(96, 96)):
        """
        Initialize the Simple Lip Cropper.
        
        Args:
            target_size: Target output size (width, height)
        """
        self.target_size = target_size
        
        # Crop parameters optimized for pre-cropped face videos
        # These ratios define the lip region within the face
        self.crop_params = {
            'y_start_ratio': 0.55,   # Start at 55% down the face (above nose)
            'y_end_ratio': 0.95,     # End at 95% down the face (below chin)
            'x_start_ratio': 0.15,   # Start at 15% from left (include cheeks)
            'x_end_ratio': 0.85,     # End at 85% from left (include cheeks)
        }
        
    def calculate_lip_crop_region(self, frame):
        """
        Calculate the lip crop region based on frame dimensions.
        
        Args:
            frame: Input frame
            
        Returns:
            tuple: (x, y, width, height) of crop region
        """
        h, w = frame.shape[:2]
        
        # Calculate crop coordinates
        y_start = int(h * self.crop_params['y_start_ratio'])
        y_end = int(h * self.crop_params['y_end_ratio'])
        x_start = int(w * self.crop_params['x_start_ratio'])
        x_end = int(w * self.crop_params['x_end_ratio'])
        
        # Ensure coordinates are within bounds
        y_start = max(0, y_start)
        y_end = min(h, y_end)
        x_start = max(0, x_start)
        x_end = min(w, x_end)
        
        # Calculate width and height
        crop_width = x_end - x_start
        crop_height = y_end - y_start
        
        # Ensure minimum size
        min_size = min(w, h) // 4
        if crop_width < min_size or crop_height < min_size:
            # Fallback to center crop if calculated region is too small
            center_x, center_y = w // 2, h // 2
            half_size = min_size // 2
            x_start = max(0, center_x - half_size)
            y_start = max(0, center_y - half_size)
            crop_width = min(min_size, w - x_start)
            crop_height = min(min_size, h - y_start)
        
        return (x_start, y_start, crop_width, crop_height)
    
    def crop_and_resize_frame(self, frame):
        """
        Crop frame to lip region and resize to target size.
        
        Args:
            frame: Input frame
            
        Returns:
            Cropped and resized frame
        """
        # Get crop region
        x, y, w, h = self.calculate_lip_crop_region(frame)
        
        # Crop the frame
        cropped = frame[y:y+h, x:x+w]
        
        if cropped.size == 0:
            return np.zeros((self.target_size[1], self.target_size[0], 3), dtype=np.uint8)
        
        # Make crop square by padding or cropping
        crop_h, crop_w = cropped.shape[:2]
        
        if crop_h != crop_w:
            # Make it square by taking the smaller dimension and center cropping
            min_dim = min(crop_h, crop_w)
            
            if crop_w > crop_h:
                # Crop width
                start_x = (crop_w - min_dim) // 2
                cropped = cropped[:, start_x:start_x + min_dim]
            else:
                # Crop height
                start_y = (crop_h - min_dim) // 2
                cropped = cropped[start_y:start_y + min_dim, :]
        
        # Resize to target size using high-quality interpolation
        resized = cv2.resize(cropped, self.target_size, interpolation=cv2.INTER_LANCZOS4)
        
        return resized
    
    def process_video(self, input_path, output_path=None):
        """
        Process a video file to crop lip regions.
        
        Args:
            input_path: Path to input video
            output_path: Path to output video (optional)
            
        Returns:
            Path to processed video
        """
        if output_path is None:
            input_path = Path(input_path)
            output_path = input_path.parent / f"{input_path.stem}_simple_lip_cropped{input_path.suffix}"
        
        # Open input video
        cap = cv2.VideoCapture(str(input_path))
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {input_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Processing video: {input_path}")
        print(f"Total frames: {total_frames}, FPS: {fps}")
        print(f"Crop parameters: {self.crop_params}")
        
        # Create temporary file for processed frames
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_video_path = Path(temp_dir) / "temp_output.mp4"
            
            # Initialize video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(
                str(temp_video_path),
                fourcc,
                fps,
                self.target_size
            )
            
            frame_count = 0
            
            # Process first frame to show crop region
            ret, first_frame = cap.read()
            if ret:
                x, y, w, h = self.calculate_lip_crop_region(first_frame)
                print(f"Crop region: x={x}, y={y}, width={w}, height={h}")
                print(f"Original frame size: {first_frame.shape[1]}x{first_frame.shape[0]}")
                print(f"Crop region covers: {(w/first_frame.shape[1]*100):.1f}% width, {(h/first_frame.shape[0]*100):.1f}% height")
                
                # Process first frame
                processed_frame = self.crop_and_resize_frame(first_frame)
                out.write(processed_frame)
                frame_count += 1
                
                # Save a sample of the crop region for verification
                sample_crop = first_frame[y:y+h, x:x+w]
                cv2.imwrite("sample_crop_region.png", sample_crop)
                cv2.imwrite("sample_processed_frame.png", processed_frame)
                print("Sample crop saved as: sample_crop_region.png")
                print("Sample processed frame saved as: sample_processed_frame.png")
            
            # Process remaining frames
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Crop and resize frame
                processed_frame = self.crop_and_resize_frame(frame)
                out.write(processed_frame)
                
                frame_count += 1
                if frame_count % 10 == 0:
                    print(f"Processed {frame_count}/{total_frames} frames")
            
            cap.release()
            out.release()
            
            print(f"Successfully processed all {frame_count} frames")
            
            # Use ffmpeg to ensure proper encoding
            self._finalize_video(temp_video_path, input_path, output_path, fps)
        
        return output_path
    
    def _finalize_video(self, temp_video_path, original_path, output_path, fps):
        """Use ffmpeg to properly encode the final video."""
        cmd = [
            'ffmpeg', '-y',  # Overwrite output file
            '-i', str(temp_video_path),  # Input processed video
            '-c:v', 'libx264',  # Video codec
            '-preset', 'medium',  # Encoding preset
            '-crf', '23',  # Quality setting
            '-pix_fmt', 'yuv420p',  # Pixel format for compatibility
            '-r', str(fps),  # Frame rate
            str(output_path)
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"Successfully created: {output_path}")
        except subprocess.CalledProcessError as e:
            print(f"FFmpeg error: {e}")
            raise


def main():
    parser = argparse.ArgumentParser(description='Simple rectangular lip cropping for pre-cropped face videos')
    parser.add_argument('input', help='Input video file')
    parser.add_argument('output', nargs='?', help='Output video file (optional)')
    parser.add_argument('--size', type=int, nargs=2, default=[96, 96], 
                       help='Target size (width height), default: 96 96')
    parser.add_argument('--y-start', type=float, default=0.55,
                       help='Y start ratio (0.0-1.0), default: 0.55')
    parser.add_argument('--y-end', type=float, default=0.95,
                       help='Y end ratio (0.0-1.0), default: 0.95')
    parser.add_argument('--x-start', type=float, default=0.15,
                       help='X start ratio (0.0-1.0), default: 0.15')
    parser.add_argument('--x-end', type=float, default=0.85,
                       help='X end ratio (0.0-1.0), default: 0.85')
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' not found")
        sys.exit(1)
    
    # Create cropper instance
    cropper = SimpleLipCropper(target_size=tuple(args.size))
    
    # Update crop parameters if provided
    if any([args.y_start != 0.55, args.y_end != 0.95, args.x_start != 0.15, args.x_end != 0.85]):
        cropper.crop_params = {
            'y_start_ratio': args.y_start,
            'y_end_ratio': args.y_end,
            'x_start_ratio': args.x_start,
            'x_end_ratio': args.x_end,
        }
    
    try:
        output_path = cropper.process_video(args.input, args.output)
        print(f"\nProcessing complete!")
        print(f"Input: {args.input}")
        print(f"Output: {output_path}")
        
    except Exception as e:
        print(f"Error processing video: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
