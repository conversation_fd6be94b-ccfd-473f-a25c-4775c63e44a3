# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/stream_handler/default_input_stream_handler.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import mediapipe_options_pb2 as mediapipe_dot_framework_dot_mediapipe__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nEmediapipe/framework/stream_handler/default_input_stream_handler.proto\x12\tmediapipe\x1a+mediapipe/framework/mediapipe_options.proto\"\x93\x01\n DefaultInputStreamHandlerOptions\x12\x15\n\nbatch_size\x18\x01 \x01(\x05:\x01\x31\x32X\n\x03\x65xt\x12\x1b.mediapipe.MediaPipeOptions\x18\xf5\xed\xacN \x01(\x0b\x32+.mediapipe.DefaultInputStreamHandlerOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.stream_handler.default_input_stream_handler_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_DEFAULTINPUTSTREAMHANDLEROPTIONS']._serialized_start=130
  _globals['_DEFAULTINPUTSTREAMHANDLEROPTIONS']._serialized_end=277
# @@protoc_insertion_point(module_scope)
