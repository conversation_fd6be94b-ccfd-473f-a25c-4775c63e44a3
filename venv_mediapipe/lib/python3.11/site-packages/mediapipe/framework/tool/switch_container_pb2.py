# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/tool/switch_container.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/mediapipe/framework/tool/switch_container.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\x9b\x02\n\x16SwitchContainerOptions\x12=\n\x0e\x63ontained_node\x18\x02 \x03(\x0b\x32%.mediapipe.CalculatorGraphConfig.Node\x12\x0e\n\x06select\x18\x03 \x01(\x05\x12\x0e\n\x06\x65nable\x18\x04 \x01(\x08\x12\x16\n\x0esynchronize_io\x18\x05 \x01(\x08\x12\x17\n\x0f\x61sync_selection\x18\x06 \x01(\x08\x12\x19\n\x11tick_input_stream\x18\x07 \x03(\t2P\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xe2\x9a\xfc\xa4\x01 \x01(\x0b\x32!.mediapipe.SwitchContainerOptionsJ\x04\x08\x01\x10\x02\x42\x32\n\x1a\x63om.google.mediapipe.protoB\x14SwitchContainerProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.tool.switch_container_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.mediapipe.protoB\024SwitchContainerProto'
  _globals['_SWITCHCONTAINEROPTIONS']._serialized_start=101
  _globals['_SWITCHCONTAINEROPTIONS']._serialized_end=384
# @@protoc_insertion_point(module_scope)
