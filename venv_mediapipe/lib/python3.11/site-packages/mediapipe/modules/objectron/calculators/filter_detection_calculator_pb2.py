# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/modules/objectron/calculators/filter_detection_calculator.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nImediapipe/modules/objectron/calculators/filter_detection_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xcb\x02\n FilterDetectionCalculatorOptions\x12\x11\n\tmin_score\x18\x01 \x01(\x02\x12\x11\n\tmax_score\x18\x02 \x01(\x02\x12#\n\x14\x66\x61il_on_empty_labels\x18\x03 \x01(\x08:\x05\x66\x61lse\x12:\n+empty_allowed_labels_means_allow_everything\x18\x06 \x01(\x08:\x05\x66\x61lse\x12 \n\x14use_detection_vector\x18\x04 \x01(\x08\x42\x02\x18\x01\x12\"\n\x16use_allowed_labels_csv\x18\x05 \x01(\x08\x42\x02\x18\x01\x32Z\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\x8b\xc0\xf6\xa1\x01 \x01(\x0b\x32+.mediapipe.FilterDetectionCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.modules.objectron.calculators.filter_detection_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_FILTERDETECTIONCALCULATOROPTIONS'].fields_by_name['use_detection_vector']._options = None
  _globals['_FILTERDETECTIONCALCULATOROPTIONS'].fields_by_name['use_detection_vector']._serialized_options = b'\030\001'
  _globals['_FILTERDETECTIONCALCULATOROPTIONS'].fields_by_name['use_allowed_labels_csv']._options = None
  _globals['_FILTERDETECTIONCALCULATOROPTIONS'].fields_by_name['use_allowed_labels_csv']._serialized_options = b'\030\001'
  _globals['_FILTERDETECTIONCALCULATOROPTIONS']._serialized_start=127
  _globals['_FILTERDETECTIONCALCULATOROPTIONS']._serialized_end=458
# @@protoc_insertion_point(module_scope)
