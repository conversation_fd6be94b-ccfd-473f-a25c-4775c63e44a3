# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/modules/objectron/calculators/frame_annotation_to_rect_calculator.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nQmediapipe/modules/objectron/calculators/frame_annotation_to_rect_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xbf\x01\n&FrameAnnotationToRectCalculatorOptions\x12\x19\n\roff_threshold\x18\x01 \x01(\x02:\x02\x34\x30\x12\x18\n\x0con_threshold\x18\x02 \x01(\x02:\x02\x34\x31\x32`\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\x9b\x93\x9d\xa1\x01 \x01(\x0b\x32\x31.mediapipe.FrameAnnotationToRectCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.modules.objectron.calculators.frame_annotation_to_rect_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_FRAMEANNOTATIONTORECTCALCULATOROPTIONS']._serialized_start=135
  _globals['_FRAMEANNOTATIONTORECTCALCULATOROPTIONS']._serialized_end=326
# @@protoc_insertion_point(module_scope)
