# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/video/box_detector_calculator.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.util.tracking import box_detector_pb2 as mediapipe_dot_util_dot_tracking_dot_box__detector__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9mediapipe/calculators/video/box_detector_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\x1a*mediapipe/util/tracking/box_detector.proto\"\xcd\x01\n\x1c\x42oxDetectorCalculatorOptions\x12\x37\n\x10\x64\x65tector_options\x18\x01 \x01(\x0b\x32\x1d.mediapipe.BoxDetectorOptions\x12\x1c\n\x14index_proto_filename\x18\x02 \x03(\t2V\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xe2\xdc\x94\x8a\x01 \x01(\x0b\x32\'.mediapipe.BoxDetectorCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.video.box_detector_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_BOXDETECTORCALCULATOROPTIONS']._serialized_start=155
  _globals['_BOXDETECTORCALCULATOROPTIONS']._serialized_end=360
# @@protoc_insertion_point(module_scope)
