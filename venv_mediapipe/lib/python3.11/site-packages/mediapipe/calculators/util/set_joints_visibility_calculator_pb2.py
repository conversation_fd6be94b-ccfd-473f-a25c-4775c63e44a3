# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/set_joints_visibility_calculator.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nAmediapipe/calculators/util/set_joints_visibility_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\x9f\x04\n$SetJointsVisibilityCalculatorOptions\x12H\n\x07mapping\x18\x01 \x03(\x0b\x32\x37.mediapipe.SetJointsVisibilityCalculatorOptions.Mapping\x1a\xcc\x02\n\x07Mapping\x12V\n\tunchanged\x18\x01 \x01(\x0b\x32\x41.mediapipe.SetJointsVisibilityCalculatorOptions.Mapping.UnchangedH\x00\x12L\n\x04\x63opy\x18\x02 \x01(\x0b\x32<.mediapipe.SetJointsVisibilityCalculatorOptions.Mapping.CopyH\x00\x12R\n\x07highest\x18\x03 \x01(\x0b\x32?.mediapipe.SetJointsVisibilityCalculatorOptions.Mapping.HighestH\x00\x1a\x0b\n\tUnchanged\x1a\x13\n\x04\x43opy\x12\x0b\n\x03idx\x18\x01 \x01(\x05\x1a\x1a\n\x07Highest\x12\x0f\n\x03idx\x18\x01 \x03(\x05\x42\x02\x10\x01\x42\t\n\x07mapping2^\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xfa\x91\xe7\xc1\x01 \x01(\x0b\x32/.mediapipe.SetJointsVisibilityCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.set_joints_visibility_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING_HIGHEST'].fields_by_name['idx']._options = None
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING_HIGHEST'].fields_by_name['idx']._serialized_options = b'\020\001'
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS']._serialized_start=119
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS']._serialized_end=662
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING']._serialized_start=234
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING']._serialized_end=566
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING_UNCHANGED']._serialized_start=495
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING_UNCHANGED']._serialized_end=506
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING_COPY']._serialized_start=508
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING_COPY']._serialized_end=527
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING_HIGHEST']._serialized_start=529
  _globals['_SETJOINTSVISIBILITYCALCULATOROPTIONS_MAPPING_HIGHEST']._serialized_end=555
# @@protoc_insertion_point(module_scope)
