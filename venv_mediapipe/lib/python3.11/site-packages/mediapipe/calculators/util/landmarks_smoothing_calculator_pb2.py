# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/landmarks_smoothing_calculator.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_options_pb2 as mediapipe_dot_framework_dot_calculator__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?mediapipe/calculators/util/landmarks_smoothing_calculator.proto\x12\tmediapipe\x1a,mediapipe/framework/calculator_options.proto\"\xf5\x05\n#LandmarksSmoothingCalculatorOptions\x12L\n\tno_filter\x18\x01 \x01(\x0b\x32\x37.mediapipe.LandmarksSmoothingCalculatorOptions.NoFilterH\x00\x12X\n\x0fvelocity_filter\x18\x02 \x01(\x0b\x32=.mediapipe.LandmarksSmoothingCalculatorOptions.VelocityFilterH\x00\x12W\n\x0fone_euro_filter\x18\x03 \x01(\x0b\x32<.mediapipe.LandmarksSmoothingCalculatorOptions.OneEuroFilterH\x00\x1a\n\n\x08NoFilter\x1a\x93\x01\n\x0eVelocityFilter\x12\x16\n\x0bwindow_size\x18\x01 \x01(\x05:\x01\x35\x12\x1a\n\x0evelocity_scale\x18\x02 \x01(\x02:\x02\x31\x30\x12\'\n\x18min_allowed_object_scale\x18\x03 \x01(\x02:\x05\x31\x65-06\x12$\n\x15\x64isable_value_scaling\x18\x04 \x01(\x08:\x05\x66\x61lse\x1a\xb9\x01\n\rOneEuroFilter\x12\x15\n\tfrequency\x18\x01 \x01(\x02:\x02\x33\x30\x12\x15\n\nmin_cutoff\x18\x02 \x01(\x02:\x01\x31\x12\x0f\n\x04\x62\x65ta\x18\x03 \x01(\x02:\x01\x30\x12\x1a\n\x0f\x64\x65rivate_cutoff\x18\x04 \x01(\x02:\x01\x31\x12\'\n\x18min_allowed_object_scale\x18\x05 \x01(\x02:\x05\x31\x65-06\x12$\n\x15\x64isable_value_scaling\x18\x06 \x01(\x08:\x05\x66\x61lse2]\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\x85\xb4\xa5\x9b\x01 \x01(\x0b\x32..mediapipe.LandmarksSmoothingCalculatorOptionsB\x10\n\x0e\x66ilter_options')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.landmarks_smoothing_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_LANDMARKSSMOOTHINGCALCULATOROPTIONS']._serialized_start=125
  _globals['_LANDMARKSSMOOTHINGCALCULATOROPTIONS']._serialized_end=882
  _globals['_LANDMARKSSMOOTHINGCALCULATOROPTIONS_NOFILTER']._serialized_start=421
  _globals['_LANDMARKSSMOOTHINGCALCULATOROPTIONS_NOFILTER']._serialized_end=431
  _globals['_LANDMARKSSMOOTHINGCALCULATOROPTIONS_VELOCITYFILTER']._serialized_start=434
  _globals['_LANDMARKSSMOOTHINGCALCULATOROPTIONS_VELOCITYFILTER']._serialized_end=581
  _globals['_LANDMARKSSMOOTHINGCALCULATOROPTIONS_ONEEUROFILTER']._serialized_start=584
  _globals['_LANDMARKSSMOOTHINGCALCULATOROPTIONS_ONEEUROFILTER']._serialized_end=769
# @@protoc_insertion_point(module_scope)
