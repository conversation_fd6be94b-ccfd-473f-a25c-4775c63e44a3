# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/tensor/tensors_readback_calculator.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>mediapipe/calculators/tensor/tensors_readback_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xee\x01\n TensorsReadbackCalculatorOptions\x12M\n\x0ctensor_shape\x18\x01 \x03(\x0b\x32\x37.mediapipe.TensorsReadbackCalculatorOptions.TensorShape\x1a\x1f\n\x0bTensorShape\x12\x10\n\x04\x64ims\x18\x01 \x03(\x05\x42\x02\x10\x01\x32Z\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xa4\xef\xb9\xf5\x01 \x01(\x0b\x32+.mediapipe.TensorsReadbackCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.tensor.tensors_readback_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_TENSORSREADBACKCALCULATOROPTIONS_TENSORSHAPE'].fields_by_name['dims']._options = None
  _globals['_TENSORSREADBACKCALCULATOROPTIONS_TENSORSHAPE'].fields_by_name['dims']._serialized_options = b'\020\001'
  _globals['_TENSORSREADBACKCALCULATOROPTIONS']._serialized_start=116
  _globals['_TENSORSREADBACKCALCULATOROPTIONS']._serialized_end=354
  _globals['_TENSORSREADBACKCALCULATOROPTIONS_TENSORSHAPE']._serialized_start=231
  _globals['_TENSORSREADBACKCALCULATOROPTIONS_TENSORSHAPE']._serialized_end=262
# @@protoc_insertion_point(module_scope)
