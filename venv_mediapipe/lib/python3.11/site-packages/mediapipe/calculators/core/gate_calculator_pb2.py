# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/core/gate_calculator.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0mediapipe/calculators/core/gate_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xd2\x02\n\x15GateCalculatorOptions\x12\x1e\n\x16\x65mpty_packets_as_allow\x18\x01 \x01(\x08\x12\x14\n\x05\x61llow\x18\x02 \x01(\x08:\x05\x66\x61lse\x12Z\n\x12initial_gate_state\x18\x03 \x01(\x0e\x32*.mediapipe.GateCalculatorOptions.GateState:\x12GATE_UNINITIALIZED\"W\n\tGateState\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x16\n\x12GATE_UNINITIALIZED\x10\x01\x12\x0e\n\nGATE_ALLOW\x10\x02\x12\x11\n\rGATE_DISALLOW\x10\x03\x32N\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xdf\x9f\xe8| \x01(\x0b\x32 .mediapipe.GateCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.core.gate_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_GATECALCULATOROPTIONS']._serialized_start=102
  _globals['_GATECALCULATOROPTIONS']._serialized_end=440
  _globals['_GATECALCULATOROPTIONS_GATESTATE']._serialized_start=273
  _globals['_GATECALCULATOROPTIONS_GATESTATE']._serialized_end=360
# @@protoc_insertion_point(module_scope)
